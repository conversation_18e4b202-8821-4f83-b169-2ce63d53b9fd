import { useQuery, useQueryClient } from '@tanstack/react-query'
import type { RewardSet } from '../types'
import { fetchCampaignRewardSet } from './api'

export const CAMPAIGN_REWARD_SET_KEY = ['campaign-reward-set'] as const

export function useCampaignRewardSet() {
  const queryClient = useQueryClient()

  const { data, isLoading, error } = useQuery<RewardSet>({
    queryKey: CAMPAIGN_REWARD_SET_KEY,
    queryFn: () => fetchCampaignRewardSet(),
  })

  const setRewardSet = (next: RewardSet) => {
    queryClient.setQueryData<RewardSet>(CAMPAIGN_REWARD_SET_KEY, next)
  }

  return {
    rewardSet: data ?? null,
    setRewardSet,
    isLoading,
    error,
  }
}

