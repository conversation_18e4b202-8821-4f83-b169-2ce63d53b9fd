import type { RewardType, EditorComponent } from './types'
import { CouponCodeReusableEditor } from './components/couponCodeReusableEditor'
import { CouponCodeMultipleEditor } from './components/couponCodeMultipleEditor'

export const REWARDS_REGISTRY: Record<RewardType, { id: RewardType, Editor: EditorComponent }> = {
  'coupon-code-reusable': { id: 'coupon-code-reusable', Editor: CouponCodeReusableEditor },
  'coupon-code-multiple': { id: 'coupon-code-multiple', Editor: CouponCodeMultipleEditor },
} as const

export const getEditorForType = (t: RewardType) => REWARDS_REGISTRY[t].Editor


