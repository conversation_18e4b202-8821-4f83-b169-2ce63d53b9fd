import React from 'react'
import type { RewardDefinition } from '../types'
import { Input } from '@repo/shared/components/ui/input'
import { Label } from '@repo/shared/components/ui/label'
import type { Settings as CouponCodeReusableSettings } from '../reward-types/couponCodeReusable'

interface EditorProps {
  reward: RewardDefinition
  onChange: (next: RewardDefinition) => void
}

export const CouponCodeReusableEditor: React.FC<EditorProps> = ({ reward, onChange }) => {
  const settings = reward.settings as CouponCodeReusableSettings

  const update = (changes: Partial<CouponCodeReusableSettings>) => {
    onChange({ ...reward, settings: { ...settings, ...changes } })
  }

  return (
    <div className="space-y-3">
      <div className="space-y-2">
        <Label htmlFor="reward-name">Reward Name</Label>
        <Input
          id="reward-name"
          placeholder="Enter reward name"
          value={reward.name}
          onChange={(e) => onChange({ ...reward, name: e.target.value })}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="reward-desc">Description (Optional)</Label>
        <Input
          id="reward-desc"
          placeholder="Enter reward description"
          value={reward.description}
          onChange={(e) => onChange({ ...reward, description: e.target.value })}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="reusable-code">Reusable Code</Label>
        <Input
          id="reusable-code"
          placeholder="e.g. SAVE20"
          value={settings.reusableCode}
          onChange={(e) => update({ reusableCode: e.target.value })}
        />
      </div>
    </div>
  )
}

export default CouponCodeReusableEditor


