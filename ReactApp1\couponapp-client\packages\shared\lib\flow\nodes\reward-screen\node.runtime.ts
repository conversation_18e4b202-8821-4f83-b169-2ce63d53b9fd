import { fetchRewardById } from '@repo/shared/features/rewards/lib/api';
import type { NodeRuntimeContext, NodeRunResult, NodeChildRef } from '../../types'
import type { Settings } from './node.types'

export async function run({ ctx, settings, children }: { ctx: NodeRuntimeContext; settings: Settings; children?: NodeChildRef[] }): Promise<NodeRunResult> {
	const edge = ctx.inputEdges[0]
	
	if(!edge) {
		console.log("No edge found. Not showing reward screen.")
		return { next: [] }
	}

	ctx.setGameScene(ctx.graph.parentWidgetId, "custom/game-screen/loading-reward")

	const reward = (await fetchRewardById(settings.rewardId))
	const screenId = "custom/node/" + edge.id

	ctx.dynamicValues.setValue(`game/${ctx.graph.parentWidgetId}/reward`, reward)
	ctx.setGameScene(ctx.graph.parentWidgetId, screenId)
	
	return { next: children }
}



