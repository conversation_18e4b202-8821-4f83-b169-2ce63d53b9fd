import { WidgetRenderingContextNew } from "@repo/shared/lib/types/editor";
import { addWidgetMetadata } from "@repo/shared/lib/widget-metadata";
import { addPropertyControlsNew, ControlType } from "../../editor/property-controls-new";
import { CSSProperties } from "react";
import { motion } from "framer-motion";
import { useFramerEffects } from "@repo/shared/lib/hooks/useFramerEffects";
import DynamicSpan from "../../DynamicSpan";

export const TextWidget: React.FC<
  WidgetRenderingContextNew<any>
> = ({ settings }) => {

  const containerStyle = {
    margin: settings.margin,
    padding: settings.padding,
  };

  const { motionSettings } = useFramerEffects({
    effects: settings.effects,
    defaultVariant: { scale: 1, opacity: 1 },
  });

  const textStyle = {
    backgroundColor: settings.backgroundColor,
    color: settings.textColor,
    fontSize: settings.fontSize,
    fontWeight: settings.fontWeight,
    fontFamily: settings.fontFamily,
    lineHeight: settings.lineHeight,
    letterSpacing: `${settings.letterSpacing}px`,
    textAlign: settings.textAlign,
    textTransform: settings.textTransform,
    textDecoration: settings.textDecoration,
    width: "100%",
    whiteSpace: "pre-wrap" as const,
  } as CSSProperties;

  return (
    <div
      className="flex heading-container justify-center"
      style={containerStyle}
    >
      <div className="w-full whitespace-pre-wrap" style={textStyle}>
        <motion.div className="inline-block" {...motionSettings}>
          <DynamicSpan dynamicText={settings.text} />
        </motion.div>
      </div>
    </div>
  );
};

addWidgetMetadata(TextWidget, {
  componentName: "TextWidget",
  displayName: "Text",
  type: "block",
  description: "A text block widget for displaying content",
  icon: "type",
});

addPropertyControlsNew(TextWidget, {
  // Content
  text: {
    type: ControlType.DynamicText,
    title: "Text",
    defaultValue: "Your Text Here"
  },

  // Styling
  backgroundColor: {
    type: ControlType.Color,
    title: "Background Color",
    defaultValue: "transparent"
  },
  textColor: {
    type: ControlType.Color,
    title: "Text Color",
    defaultValue: "#000000"
  },
  fontSize: {
    type: ControlType.Text,
    title: "Font Size",
    defaultValue: "1rem"
  },
  fontWeight: {
    type: ControlType.Text,
    title: "Font Weight",
    defaultValue: "400"
  },
  fontFamily: {
    type: ControlType.Text,
    title: "Font Family",
    defaultValue: "Poppins"
  },
  lineHeight: {
    type: ControlType.Text,
    title: "Line Height",
    defaultValue: undefined
  },
  letterSpacing: {
    type: ControlType.Number,
    title: "Letter Spacing (px)",
    defaultValue: 0
  },
  textAlign: {
    type: ControlType.Enum,
    title: "Text Align",
    defaultValue: "left",
    options: [
      { label: "Left", value: "left" },
      { label: "Center", value: "center" },
      { label: "Right", value: "right" },
      { label: "Justify", value: "justify" }
    ]
  },
  textTransform: {
    type: ControlType.Enum,
    title: "Text Transform",
    defaultValue: "none",
    options: [
      { label: "None", value: "none" },
      { label: "Uppercase", value: "uppercase" },
      { label: "Lowercase", value: "lowercase" },
      { label: "Capitalize", value: "capitalize" }
    ]
  },
  textDecoration: {
    type: ControlType.Enum,
    title: "Text Decoration",
    defaultValue: "none",
    options: [
      { label: "None", value: "none" },
      { label: "Underline", value: "underline" },
      { label: "Line Through", value: "line-through" }
    ]
  },
  padding: {
    type: ControlType.Padding,
    title: "Padding",
    defaultValue: "0px"
  },
  margin: {
    type: ControlType.Margin,
    title: "Margin",
    defaultValue: "0px"
  }
});
