import React from 'react'
import type { RewardDefinition } from '../types'
import { Label } from '@repo/shared/components/ui/label'
import { Input } from '@repo/shared/components/ui/input'
import { Textarea } from '@repo/shared/components/ui/textarea'
import { Button } from '@repo/shared/components/ui/button'
import type { Settings as CouponCodeMultipleSettings } from '../reward-types/couponCodeMultiple'

interface EditorProps {
  reward: RewardDefinition
  onChange: (next: RewardDefinition) => void
}

export const CouponCodeMultipleEditor: React.FC<EditorProps> = ({ reward, onChange }) => {
  const settings = reward.settings as CouponCodeMultipleSettings

  const [mode, setMode] = React.useState<'list' | 'add'>('list')
  const pageSize = 500
  const [page, setPage] = React.useState<number>(1)
  const MAX_CODES = 1000

  const codes = Array.isArray(settings.codes) ? settings.codes : []
  const totalPages = Math.max(1, Math.ceil((codes.length || 0) / pageSize))
  const safePage = Math.min(Math.max(1, page), totalPages)
  const startIndex = (safePage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const pageCodes = codes.slice(startIndex, endIndex)

  React.useEffect(() => {
    // Ensure current page is valid when codes change
    const nextTotalPages = Math.max(1, Math.ceil((codes.length || 0) / pageSize))
    if (page > nextTotalPages) setPage(nextTotalPages)
  }, [codes.length])

  // Add-codes subpage state
  const [newCodesInput, setNewCodesInput] = React.useState<string>('')
  const [generateCount, setGenerateCount] = React.useState<number>(10)
  const [generateLength, setGenerateLength] = React.useState<number>(8)
  const [generatePrefix, setGeneratePrefix] = React.useState<string>('')

  const toLines = (arr: string[]) => arr.join('\n')

  const parseCodes = (text: string): string[] => {
    return text
      .split(/\r?\n|,/) // support newline or comma
      .map((s) => s.trim())
      .filter((s) => s.length > 0)
  }

  const handleSaveNewCodes = () => {
    const incoming = parseCodes(newCodesInput)
    if (!incoming.length) {
      setMode('list')
      return
    }
    const existingSet = new Set(codes)
    const merged: string[] = [...codes]
    const capacityLeft = Math.max(0, MAX_CODES - merged.length)
    if (capacityLeft <= 0) {
      setMode('list')
      return
    }
    let added = 0
    for (const c of incoming) {
      if (added >= capacityLeft) break
      if (!existingSet.has(c)) {
        existingSet.add(c)
        merged.push(c)
        added += 1
      }
    }
    onChange({ ...reward, settings: { ...settings, codes: merged } })
    setNewCodesInput('')
    setMode('list')
  }

  const randomFromAlphabet = (length: number, alphabet: string) => {
    let out = ''
    const n = alphabet.length
    for (let i = 0; i < length; i++) {
      const idx = Math.floor(Math.random() * n)
      out += alphabet[idx]
    }
    return out
  }


  const capacityLeftToAdd = Math.max(0, MAX_CODES - codes.length)
  const uniqueIncomingNotExistingCount = (() => {
    const existing = new Set(codes)
    const uniq = new Set<string>()
    for (const c of parseCodes(newCodesInput)) {
      if (!existing.has(c)) uniq.add(c)
    }
    return uniq.size
  })()
  const saveWouldExceed = uniqueIncomingNotExistingCount > capacityLeftToAdd

  if (mode === 'add') {
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium text-foreground">Add Codes</div>
          <Button variant="ghost" size="sm" onClick={() => setMode('list')}>Back</Button>
        </div>
        <div className="grid grid-cols-1 gap-3">
          <div className="md:col-span-2 space-y-2">
            <Label htmlFor="new-codes">New Codes (one per line or comma-separated, max 1000)</Label>
            <Textarea
              id="new-codes"
              placeholder="ABC123"
              value={newCodesInput}
              onChange={(e) => setNewCodesInput(e.target.value)}
              className="min-h-[220px]"
            />
            <div className="text-xs text-muted-foreground">Max {MAX_CODES} total codes. You can add {capacityLeftToAdd} more unique codes.</div>
            <div className="flex items-center justify-between text-xs">
              <div className="text-muted-foreground">Detected {uniqueIncomingNotExistingCount} unique new code(s).</div>
              {saveWouldExceed ? (
                <div className="text-amber-500">Only {capacityLeftToAdd} will be added due to limit.</div>
              ) : null}
            </div>
            <div className="flex items-center justify-end gap-2 pt-1">
              <Button variant="outline" size="sm" onClick={() => setMode('list')}>Cancel</Button>
              <Button size="sm" onClick={handleSaveNewCodes} disabled={uniqueIncomingNotExistingCount === 0}>Save changes</Button>
            </div>
          </div>
       
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      <div className="space-y-2">
        <Label htmlFor="reward-name">Reward Name</Label>
        <Input
          id="reward-name"
          placeholder="Enter reward name"
          value={reward.name}
          onChange={(e) => onChange({ ...reward, name: e.target.value })}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="reward-desc">Description (Optional)</Label>
        <Input
          id="reward-desc"
          placeholder="Enter reward description"
          value={reward.description}
          onChange={(e) => onChange({ ...reward, description: e.target.value })}
        />
      </div>
      <div className="flex items-center justify-between">
        <Label htmlFor="codes">Codes</Label>
        <Button size="sm" onClick={() => setMode('add')}>Add codes</Button>
      </div>
      <Textarea
        id="codes"
        readOnly
        value={toLines(pageCodes)}
        placeholder="No codes yet"
        className="min-h-[220px]"
      />
      <div className="flex items-center justify-between">
        <div className="text-xs text-muted-foreground">Showing {pageCodes.length} of {codes.length} codes</div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setPage((p) => Math.max(1, p - 1))} disabled={safePage <= 1}>Prev</Button>
          <div className="text-xs text-muted-foreground">Page {safePage} / {totalPages}</div>
          <Button variant="outline" size="sm" onClick={() => setPage((p) => Math.min(totalPages, p + 1))} disabled={safePage >= totalPages}>Next</Button>
        </div>
      </div>
    </div>
  )
}

export default CouponCodeMultipleEditor


