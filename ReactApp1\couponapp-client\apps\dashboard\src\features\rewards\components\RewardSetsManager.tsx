import React from 'react'
import type { RewardSet } from '@repo/shared/features/rewards/types'
import RewardSetManager from '@repo/shared/features/rewards/components/RewardSetManager'
import { useCampaignRewardSet } from '@repo/shared/features/rewards/lib/useCampaignRewardSet'

export interface RewardSetsManagerProps {
  onSelect?: (set: RewardSet) => void
}

export const RewardSetsManager: React.FC<RewardSetsManagerProps> = ({ onSelect }) => {
  const { rewardSet, isLoading: loading, setRewardSet } = useCampaignRewardSet()

  React.useEffect(() => {
    if (rewardSet) onSelect?.(rewardSet)
  }, [rewardSet, onSelect])

  const handleChange = (next: RewardSet) => {
    setRewardSet(next)
  }

  return (
    <div className="w-full h-[800px] overflow-hidden bg-background p-4">
      {loading ? (
        <div className="flex items-center justify-center h-32 text-muted-foreground">
          <div className="animate-pulse">Loading rewards...</div>
        </div>
      ) : rewardSet ? (
        <div className="h-full">
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-foreground mb-2">Campaign Rewards</h2>
            <div className="text-sm text-muted-foreground">Manage rewards available for this campaign</div>
          </div>
          <RewardSetManager set={rewardSet} onChange={handleChange} />
        </div>
      ) : (
        <div className="flex items-center justify-center h-32 text-muted-foreground">
          <div className="text-center">
            <div className="text-sm">No rewards configured</div>
            <div className="text-xs mt-1">Add your first reward to get started</div>
          </div>
        </div>
      )}
    </div>
  )
}

export default RewardSetsManager

