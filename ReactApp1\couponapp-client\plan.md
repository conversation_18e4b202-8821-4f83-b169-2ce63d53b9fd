### Plan (simple but complete)

- Goals
  - Make reward types semi‑modular and shared (usable by both dashboard and shared components).
  - Per‑type files + per‑type editors; a single `REWARDS_REGISTRY` object in `packages/shared/features/rewards/registry.ts` defines types and editors.
  - Render type‑specific Editor below “Add New Reward” and inline per-row editor.

- Shared folder structure (single source of truth)
```text
packages/shared/features/rewards/
  registry.ts
  reward-types/
    couponCodeReusable.ts
    couponCodeMultiple.ts
    components/
      couponCodeReusableEditor.tsx
      couponCodeMultipleEditor.tsx
```
- Dashboard will import from this shared location. No duplicate type modules under `apps/dashboard`.

- Registry file (`packages/shared/features/rewards/registry.ts`)
  - Export a single `REWARDS_REGISTRY: Record<RewardType, { id: RewardType, Editor: React.ComponentType<EditorProps> }>`.
  - Derive available types with `Object.keys(REWARDS_REGISTRY) as RewardType[]`.
  - Optionally export helper `getEditorForType(type)`.
  - Acts as the single source for what the Dashboard can create/edit.
  - Example shape:
```ts
export const REWARDS_REGISTRY = {
  'coupon-code-reusable': { id: 'coupon-code-reusable', Editor: CouponCodeReusableEditor },
  'coupon-code-multiple': { id: 'coupon-code-multiple', Editor: CouponCodeMultipleEditor }
} as const;

export const getEditorForType = (t: RewardType) => REWARDS_REGISTRY[t].Editor;
```

- Per‑type definition module (each file eg. couponCodeReusable.ts)
  - Export:
    - TYPE_ID: 'coupon-code-reusable' | 'coupon-code-multiple' (string literal)
    - label: string
    - Settings: interface (type‑specific shape)
    - DEFAULTS: Settings
  - These live in their own files; registry only maps type to Editor. Consumers import other props (like `DEFAULTS`, `label`) directly from per‑type files when needed.

- Per‑type editor component (each file eg. couponCodeReusableEditor.tsx)
  - Props: `{ reward: RewardDefinition, onChange(next: RewardDefinition): void }`
  - Inside editor:
    - Assume `reward.type` matches the Editor’s type; use `reward.settings` (already initialized).
    - Emit immutable updates with merged `settings` via `onChange`.
  - Keep dark‑mode styling consistent.

- Shared type updates (type‑safey)
  - `packages/shared/features/rewards/types.ts`
    - Define `settings` as required and strongly typed per `RewardType` (not optional).
  - Editors rely on required `settings`; no fallback/defaulting inside editors.

- Update both RewardSetManagers to use the shared modules
  - apps/dashboard/src/features/rewards/components/RewardSetManager.tsx
  - packages/shared/features/rewards/components/RewardSetManager.tsx
  - Replace hardcoded list with `Object.keys(REWARDS_REGISTRY) as RewardType[]`.
  - “Add New Reward” view (both)
    - Render the corresponding type Editor via `REWARDS_REGISTRY[newReward.type].Editor`.
    - Initialize default `settings` in the manager using the type's `DEFAULTS` when creating; editors assume `settings` exists.
  - List view (both)
    - Add an inline row editor toggle.
    - Render the matching per‑type Editor via the registry and persist changes back to `local.rewards`.
  - Important: Only apply per‑type editors when the set is a “dashboard” set (i.e., `!isGameRewardSet`). For game reward sets (`'coupon' | 'gift_card'`), leave behavior unchanged for now.