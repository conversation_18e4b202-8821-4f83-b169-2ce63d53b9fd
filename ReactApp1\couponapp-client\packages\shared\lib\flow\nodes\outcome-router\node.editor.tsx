import React, { useEffect, useState } from 'react'
import { registerNode } from '../../registry'
import type { NodeDefinition, NodeEditorBodyProps, NodeEditorPropertiesProps, NodeOutputPort } from '../../types'
import type { Settings, Outcome, OutcomeCondition, ConditionType, GameResult } from './node.types'
import { run } from './node.runtime'
import { Button } from '@repo/shared/components/ui/button'
import { Badge } from '@repo/shared/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@repo/shared/components/ui/popover'
import { Input } from '@repo/shared/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { Label } from '@repo/shared/components/ui/label'
import { createShortUuid } from '../../../utils'

function ConditionBadge({ condition, onRemove }: { condition: OutcomeCondition; onRemove: () => void }) {
  const text = condition.type === 'scoreGte' ? `score ≥ ${condition.value}` : `game ${String(condition.value)}`
  return (
    <div className="flex items-center gap-1">
      <Badge variant="secondary">{text}</Badge>
      <button className="text-xs text-muted-foreground hover:text-foreground" onClick={onRemove} aria-label="Remove condition">×</button>
    </div>
  )
}

function OutcomeEditor({ outcome, onChange, onDelete }: { outcome: Outcome; onChange: (o: Outcome) => void; onDelete: () => void }) {
  const [open, setOpen] = useState(false)
  const [condType, setCondType] = useState<ConditionType>('scoreGte')
  const [score, setScore] = useState<number>(0)
  const [res, setRes] = useState<GameResult>('win')

  // Ensure fallback never has conditions
  useEffect(() => {
    if (outcome.isFallback && outcome.conditions.length > 0) {
      onChange({ ...outcome, conditions: [] })
    }
  }, [outcome.isFallback, outcome.conditions, onChange])

  const addCondition = () => {
    if (outcome.isFallback) return
    const newCondition: OutcomeCondition = {
      id: createShortUuid(),
      type: condType,
      value: condType === 'scoreGte' ? Number(score) : res,
    }
    onChange({ ...outcome, conditions: [...outcome.conditions, newCondition] })
    setOpen(false)
  }

  return (
    <div className="border rounded p-2 space-y-2">
      <div className="flex items-center gap-2 justify-between">
        <Label className="h-8" >{outcome.label}</Label>
        {!outcome.isFallback && (
          <Button variant="destructive" size="sm" onClick={onDelete}>Delete</Button>
        )}
      </div>
      <div className="flex flex-wrap gap-2">
        {!outcome.isFallback && outcome.conditions.map((c) => (
          <ConditionBadge key={c.id} condition={c} onRemove={() => onChange({ ...outcome, conditions: outcome.conditions.filter((x) => x.id !== c.id) })} />
        ))}
        {!outcome.isFallback && (
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm">Add condition</Button>
            </PopoverTrigger>
            <PopoverContent>
              <div className="space-y-3">
                <div className="space-y-1">
                  <Label>Condition</Label>
                  <Select value={condType} onValueChange={(v) => setCondType(v as ConditionType)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="scoreGte">score ≥ than</SelectItem>
                      <SelectItem value="gameResult">game result is</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {condType === 'scoreGte' ? (
                  <div className="space-y-1">
                    <Label>Score</Label>
                    <Input type="number" value={String(score)} onChange={(e) => setScore(Number(e.target.value))} />
                  </div>
                ) : (
                  <div className="space-y-1">
                    <Label>Result</Label>
                    <Select value={res} onValueChange={(v) => setRes(v as GameResult)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="win">win</SelectItem>
                        <SelectItem value="lose">lose</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
                <div className="flex justify-end">
                  <Button onClick={addCondition}>Add</Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        )}
      </div>
    </div>
  )
}

function OutcomeRouterNodeBody(_: NodeEditorBodyProps) {
  return <div className='w-[300px]'></div>
}

function OutcomeRouterProperties({ settings, onChange }: NodeEditorPropertiesProps<Settings>) {
  const outcomes = settings?.outcomes ?? []

  // Ensure a non-deletable default fallback outcome always exists
  useEffect(() => {
    if (!outcomes.some((o) => o.isFallback)) {
      onChange({
        outcomes: [
          ...outcomes,
          { id: createShortUuid(), label: 'Fallback', conditions: [], isFallback: true },
        ],
      })
    }
  }, [outcomes, onChange])

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <div className="font-medium text-xs">Outcomes</div>
      </div>
      <div className="space-y-2">
        {outcomes.map((o) => (
          <OutcomeEditor
            key={o.id}
            outcome={o}
            onChange={(next) => onChange({ outcomes: outcomes.map((x) => (x.id === o.id ? next : x)) })}
            onDelete={() => {
              if (o.isFallback) return
              onChange({ outcomes: outcomes.filter((x) => x.id !== o.id) })
            }}
          />
        ))}
      </div>
      <div className="pt-2">
        <Button
          size="sm"
          variant="outline"
          onClick={() => {
            const nonFallbackCount = outcomes.filter((x) => !x.isFallback).length
            onChange({
              outcomes: [
                ...outcomes,
                { id: createShortUuid(), label: `Outcome ${nonFallbackCount + 1}`, conditions: [] },
              ],
            })
          }}
        >
          Add outcome
        </Button>
      </div>
    </div>
  )
}

const def: NodeDefinition<Settings> = {
  type: 'trigger:OutcomeRouter',
  label: 'Game Outcome Manager',
  icon: 'BranchIcon',
  inputs: () => [],
  outputs: (settings) => {
    const outcomes = settings?.outcomes ?? []
    const ports: NodeOutputPort[] = outcomes.map((o, idx) => ({
      key: `out_${o.id}`,
      label: o.label || `Outcome ${idx + 1}`,
      kind: 'game-outcome',
      endLabel: o.label || `Outcome ${idx + 1}`,
    }))
    return ports
  },
  editor: {
    renderNodeBody: OutcomeRouterNodeBody,
    renderProperties: OutcomeRouterProperties,
    onCreate: ({ createId }) => ({ settings: { outcomes: [{ id: createId(), label: 'Fallback', conditions: [], isFallback: true }] } }),
    onDelete: () => {},
    placeholderNodes: (ctx) => {
        return [
            {
              key: 'out_0',
              render: () => <div>Placeholder</div>,
            },
        ]
    },
  },
  runtime: { run },
}

registerNode(def)
export default def


